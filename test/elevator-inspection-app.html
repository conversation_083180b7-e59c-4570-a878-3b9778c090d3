<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电梯检验管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        warning: '#F59E0B',
                        danger: '#EF4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-sans">
    <!-- 顶部状态栏 -->
    <div class="bg-white px-4 py-2 flex justify-between items-center text-sm">
        <span class="font-medium">12:00</span>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-gray-600"></i>
            <i class="fas fa-wifi text-gray-600"></i>
            <i class="fas fa-battery-full text-gray-600"></i>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="min-h-screen pb-20">
        <!-- 头部 -->
        <header class="bg-white px-4 py-4 shadow-sm">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-lg font-bold text-gray-800">张三</h1>
                    <p class="text-sm text-gray-600">科大国创软件股份有限公司</p>
                </div>
                <div class="relative">
                    <i class="fas fa-bell text-2xl text-primary"></i>
                    <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">99</span>
                </div>
            </div>
        </header>

        <!-- 快捷办事 -->
        <section id="quick-services" class="bg-white mx-4 mt-4 rounded-lg shadow-sm p-4">
            <div class="flex items-center mb-4">
                <div class="w-1 h-5 bg-primary rounded mr-2"></div>
                <h2 class="text-lg font-semibold text-gray-800">快捷办事</h2>
            </div>
            
            <div class="grid grid-cols-3 gap-4">
                <div class="flex flex-col items-center p-3 bg-blue-50 rounded-lg cursor-pointer hover:bg-blue-100 transition-colors" onclick="navigateToSection('inspection-report')">
                    <i class="fas fa-clipboard-check text-2xl text-primary mb-2"></i>
                    <span class="text-sm text-gray-700 text-center">检验<br>申报</span>
                </div>
                
                <div class="flex flex-col items-center p-3 bg-green-50 rounded-lg cursor-pointer hover:bg-green-100 transition-colors" onclick="navigateToSection('elevator-inspection')">
                    <i class="fas fa-building text-2xl text-secondary mb-2"></i>
                    <span class="text-sm text-gray-700 text-center">电梯检<br>测申报</span>
                </div>
                
                <div class="flex flex-col items-center p-3 bg-blue-50 rounded-lg cursor-pointer hover:bg-blue-100 transition-colors" onclick="navigateToSection('equipment-report')">
                    <i class="fas fa-tools text-2xl text-primary mb-2"></i>
                    <span class="text-sm text-gray-700 text-center">无设备<br>申报</span>
                </div>
                
                <div class="flex flex-col items-center p-3 bg-yellow-50 rounded-lg cursor-pointer hover:bg-yellow-100 transition-colors" onclick="navigateToSection('recheck-report')">
                    <i class="fas fa-redo text-2xl text-warning mb-2"></i>
                    <span class="text-sm text-gray-700 text-center">复检<br>申报</span>
                </div>
                
                <div class="flex flex-col items-center p-3 bg-orange-50 rounded-lg cursor-pointer hover:bg-orange-100 transition-colors" onclick="navigateToSection('certificate-exchange')">
                    <i class="fas fa-exchange-alt text-2xl text-orange-500 mb-2"></i>
                    <span class="text-sm text-gray-700 text-center">电梯检测合<br>格证换发</span>
                </div>
                
                <div class="flex flex-col items-center p-3 bg-red-50 rounded-lg cursor-pointer hover:bg-red-100 transition-colors" onclick="navigateToSection('certificate-modify')">
                    <i class="fas fa-edit text-2xl text-danger mb-2"></i>
                    <span class="text-sm text-gray-700 text-center">电梯检测合<br>格证修改</span>
                </div>
            </div>
        </section>

        <!-- 综合查询 -->
        <section id="comprehensive-query" class="bg-white mx-4 mt-4 rounded-lg shadow-sm p-4">
            <div class="flex items-center mb-4">
                <div class="w-1 h-5 bg-primary rounded mr-2"></div>
                <h2 class="text-lg font-semibold text-gray-800">综合查询</h2>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
                <div class="relative">
                    <div class="flex items-center p-4 bg-blue-50 rounded-lg cursor-pointer hover:bg-blue-100 transition-colors" onclick="navigateToSection('report-query')">
                        <i class="fas fa-search text-2xl text-primary mr-3"></i>
                        <span class="text-sm text-gray-700">申报查询</span>
                    </div>
                    <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">5</span>
                </div>
                
                <div class="flex items-center p-4 bg-blue-50 rounded-lg cursor-pointer hover:bg-blue-100 transition-colors" onclick="navigateToSection('report-inquiry')">
                    <i class="fas fa-file-alt text-2xl text-primary mr-3"></i>
                    <span class="text-sm text-gray-700">报告查询</span>
                </div>
                
                <div class="relative">
                    <div class="flex items-center p-4 bg-blue-50 rounded-lg cursor-pointer hover:bg-blue-100 transition-colors" onclick="navigateToSection('qualification-query')">
                        <i class="fas fa-certificate text-2xl text-primary mr-3"></i>
                        <span class="text-sm text-gray-700">合格证查询</span>
                    </div>
                    <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">5</span>
                </div>
                
                <div class="relative">
                    <div class="flex items-center p-4 bg-blue-50 rounded-lg cursor-pointer hover:bg-blue-100 transition-colors" onclick="navigateToSection('notice-query')">
                        <i class="fas fa-bell text-2xl text-primary mr-3"></i>
                        <span class="text-sm text-gray-700">意见书查询</span>
                    </div>
                    <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
                </div>
                
                <div class="relative">
                    <div class="flex items-center p-4 bg-blue-50 rounded-lg cursor-pointer hover:bg-blue-100 transition-colors" onclick="navigateToSection('contact-query')">
                        <i class="fas fa-address-book text-2xl text-primary mr-3"></i>
                        <span class="text-sm text-gray-700">联络查询</span>
                    </div>
                    <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">1</span>
                </div>
                
                <div class="flex items-center p-4 bg-blue-50 rounded-lg cursor-pointer hover:bg-blue-100 transition-colors" onclick="navigateToSection('equipment-query')">
                    <i class="fas fa-cogs text-2xl text-primary mr-3"></i>
                    <span class="text-sm text-gray-700">设备查询</span>
                </div>
            </div>
        </section>
    </div>

    <!-- 底部导航 -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
        <div class="flex justify-around items-center">
            <div class="flex flex-col items-center py-2 cursor-pointer" onclick="scrollToTop()">
                <i class="fas fa-home text-2xl text-primary"></i>
                <span class="text-xs text-primary mt-1">首页</span>
                <div class="w-8 h-1 bg-primary rounded-full mt-1"></div>
            </div>
            <div class="flex flex-col items-center py-2 cursor-pointer" onclick="navigateToSection('profile')">
                <i class="fas fa-user text-2xl text-gray-400"></i>
                <span class="text-xs text-gray-400 mt-1">个人</span>
            </div>
        </div>
    </nav>

    <script>
        function navigateToSection(sectionId) {
            // 这里可以添加页面跳转逻辑
            console.log('导航到:', sectionId);
            
            // 模拟页面跳转效果
            document.body.style.opacity = '0.7';
            setTimeout(() => {
                document.body.style.opacity = '1';
                alert(`正在跳转到 ${getSectionName(sectionId)} 页面`);
            }, 200);
        }
        
        function getSectionName(sectionId) {
            const names = {
                'inspection-report': '检验申报',
                'elevator-inspection': '电梯检测申报',
                'equipment-report': '无设备申报',
                'recheck-report': '复检申报',
                'certificate-exchange': '电梯检测合格证换发',
                'certificate-modify': '电梯检测合格证修改',
                'report-query': '申报查询',
                'report-inquiry': '报告查询',
                'qualification-query': '合格证查询',
                'notice-query': '意见书查询',
                'contact-query': '联络查询',
                'equipment-query': '设备查询',
                'profile': '个人中心'
            };
            return names[sectionId] || sectionId;
        }
        
        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
        
        // 添加页面滚动效果
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('section');
            sections.forEach(section => {
                const rect = section.getBoundingClientRect();
                if (rect.top < window.innerHeight && rect.bottom > 0) {
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }
            });
        });
        
        // 初始化动画
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('section');
            sections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                section.style.transition = 'all 0.5s ease';
                
                setTimeout(() => {
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
