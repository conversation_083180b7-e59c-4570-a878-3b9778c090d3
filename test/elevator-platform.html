<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电梯检测管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        warning: '#F59E0B',
                        danger: '#EF4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-sans">
    <!-- 状态栏 -->
    <div class="bg-white px-4 py-2 flex justify-between items-center text-sm font-medium">
        <span>12:00</span>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-gray-700"></i>
            <i class="fas fa-wifi text-gray-700"></i>
            <i class="fas fa-battery-full text-gray-700"></i>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="min-h-screen pb-20">
        <!-- 首页 -->
        <div id="home" class="page active">
            <!-- 头部 -->
            <div class="bg-white px-4 py-4 shadow-sm">
                <div class="flex justify-between items-center">
                    <div>
                        <h1 class="text-lg font-bold text-gray-800">张三</h1>
                        <p class="text-sm text-gray-600">科大国创软件股份有限公司</p>
                    </div>
                    <div class="relative">
                        <i class="fas fa-bell text-2xl text-primary"></i>
                        <span class="absolute -top-2 -right-2 bg-danger text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">99</span>
                    </div>
                </div>
            </div>

            <!-- 快捷办事 -->
            <div class="px-4 py-6">
                <div class="flex items-center mb-4">
                    <div class="w-1 h-6 bg-primary rounded mr-3"></div>
                    <h2 class="text-lg font-semibold text-gray-800">快捷办事</h2>
                </div>
                
                <div class="grid grid-cols-3 gap-4 mb-8">
                    <div class="bg-white rounded-lg p-4 shadow-sm text-center" onclick="navigateTo('inspection-report')">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-clipboard-check text-primary text-xl"></i>
                        </div>
                        <p class="text-sm text-gray-700 font-medium">检验申报</p>
                    </div>
                    
                    <div class="bg-white rounded-lg p-4 shadow-sm text-center" onclick="navigateTo('elevator-inspection')">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-building text-secondary text-xl"></i>
                        </div>
                        <p class="text-sm text-gray-700 font-medium">电梯检测申报</p>
                    </div>
                    
                    <div class="bg-white rounded-lg p-4 shadow-sm text-center" onclick="navigateTo('equipment-report')">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-tools text-primary text-xl"></i>
                        </div>
                        <p class="text-sm text-gray-700 font-medium">无设备申报</p>
                    </div>
                    
                    <div class="bg-white rounded-lg p-4 shadow-sm text-center" onclick="navigateTo('recheck-report')">
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-redo text-warning text-xl"></i>
                        </div>
                        <p class="text-sm text-gray-700 font-medium">复检申报</p>
                    </div>
                    
                    <div class="bg-white rounded-lg p-4 shadow-sm text-center" onclick="navigateTo('certificate-change')">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-exchange-alt text-orange-500 text-xl"></i>
                        </div>
                        <p class="text-sm text-gray-700 font-medium">电梯检测合格证换发</p>
                    </div>
                    
                    <div class="bg-white rounded-lg p-4 shadow-sm text-center" onclick="navigateTo('certificate-modify')">
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-edit text-danger text-xl"></i>
                        </div>
                        <p class="text-sm text-gray-700 font-medium">电梯检测合格证修改</p>
                    </div>
                </div>

                <!-- 综合查询 -->
                <div class="flex items-center mb-4">
                    <div class="w-1 h-6 bg-primary rounded mr-3"></div>
                    <h2 class="text-lg font-semibold text-gray-800">综合查询</h2>
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-white rounded-lg p-4 shadow-sm" onclick="navigateTo('report-query')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-search text-primary"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-700">申报查询</span>
                            </div>
                            <span class="bg-danger text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">5</span>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg p-4 shadow-sm" onclick="navigateTo('report-inquiry')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-file-alt text-primary"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-700">报告查询</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg p-4 shadow-sm" onclick="navigateTo('qualification-query')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-certificate text-primary"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-700">合格证查询</span>
                            </div>
                            <span class="bg-danger text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">5</span>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg p-4 shadow-sm" onclick="navigateTo('notice-query')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-bell text-primary"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-700">意见书查询</span>
                            </div>
                            <span class="bg-danger text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg p-4 shadow-sm" onclick="navigateTo('contact-query')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-address-book text-primary"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-700">联络查询</span>
                            </div>
                            <span class="bg-danger text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">1</span>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg p-4 shadow-sm" onclick="navigateTo('equipment-query')">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-cogs text-primary"></i>
                            </div>
                            <span class="text-sm font-medium text-gray-700">设备查询</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 检验申报页面 -->
        <div id="inspection-report" class="page hidden">
            <div class="bg-white px-4 py-4 shadow-sm flex items-center">
                <i class="fas fa-arrow-left text-xl text-gray-600 mr-4" onclick="navigateTo('home')"></i>
                <h1 class="text-lg font-semibold">检验申报</h1>
            </div>

            <div class="px-4 py-6">
                <div class="bg-white rounded-lg p-4 shadow-sm mb-4">
                    <h3 class="font-semibold text-gray-800 mb-4">申报信息</h3>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备编号</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" placeholder="请输入设备编号">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备类型</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                                <option>请选择设备类型</option>
                                <option>乘客电梯</option>
                                <option>载货电梯</option>
                                <option>自动扶梯</option>
                                <option>自动人行道</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">使用单位</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" placeholder="请输入使用单位">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">联系人</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" placeholder="请输入联系人">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">联系电话</label>
                            <input type="tel" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" placeholder="请输入联系电话">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">预约检验时间</label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                    </div>

                    <div class="mt-6 space-y-3">
                        <button class="w-full bg-primary text-white py-3 rounded-lg font-medium" onclick="submitInspection()">
                            <i class="fas fa-paper-plane mr-2"></i>提交申报
                        </button>
                        <button class="w-full bg-gray-200 text-gray-700 py-3 rounded-lg font-medium" onclick="saveDraft()">
                            <i class="fas fa-save mr-2"></i>保存草稿
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 申报查询页面 -->
        <div id="report-query" class="page hidden">
            <div class="bg-white px-4 py-4 shadow-sm flex items-center">
                <i class="fas fa-arrow-left text-xl text-gray-600 mr-4" onclick="navigateTo('home')"></i>
                <h1 class="text-lg font-semibold">申报查询</h1>
            </div>

            <div class="px-4 py-4">
                <!-- 搜索栏 -->
                <div class="bg-white rounded-lg p-4 shadow-sm mb-4">
                    <div class="flex space-x-2">
                        <input type="text" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" placeholder="输入设备编号或使用单位">
                        <button class="bg-primary text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- 筛选标签 -->
                <div class="flex space-x-2 mb-4 overflow-x-auto">
                    <button class="bg-primary text-white px-4 py-2 rounded-full text-sm whitespace-nowrap">全部</button>
                    <button class="bg-gray-200 text-gray-700 px-4 py-2 rounded-full text-sm whitespace-nowrap">待审核</button>
                    <button class="bg-gray-200 text-gray-700 px-4 py-2 rounded-full text-sm whitespace-nowrap">已通过</button>
                    <button class="bg-gray-200 text-gray-700 px-4 py-2 rounded-full text-sm whitespace-nowrap">已拒绝</button>
                </div>

                <!-- 申报列表 -->
                <div class="space-y-3">
                    <div class="bg-white rounded-lg p-4 shadow-sm" onclick="showReportDetail('001')">
                        <div class="flex justify-between items-start mb-2">
                            <h4 class="font-semibold text-gray-800">设备编号: EL001</h4>
                            <span class="bg-warning text-white px-2 py-1 rounded text-xs">待审核</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-1">使用单位: 万达广场</p>
                        <p class="text-sm text-gray-600 mb-2">申报时间: 2024-01-15 10:30</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-500">乘客电梯</span>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg p-4 shadow-sm" onclick="showReportDetail('002')">
                        <div class="flex justify-between items-start mb-2">
                            <h4 class="font-semibold text-gray-800">设备编号: EL002</h4>
                            <span class="bg-secondary text-white px-2 py-1 rounded text-xs">已通过</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-1">使用单位: 银泰中心</p>
                        <p class="text-sm text-gray-600 mb-2">申报时间: 2024-01-14 14:20</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-500">载货电梯</span>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg p-4 shadow-sm" onclick="showReportDetail('003')">
                        <div class="flex justify-between items-start mb-2">
                            <h4 class="font-semibold text-gray-800">设备编号: EL003</h4>
                            <span class="bg-danger text-white px-2 py-1 rounded text-xs">已拒绝</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-1">使用单位: 恒隆广场</p>
                        <p class="text-sm text-gray-600 mb-2">申报时间: 2024-01-13 09:15</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-500">自动扶梯</span>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 报告详情页面 -->
        <div id="report-detail" class="page hidden">
            <div class="bg-white px-4 py-4 shadow-sm flex items-center">
                <i class="fas fa-arrow-left text-xl text-gray-600 mr-4" onclick="navigateTo('report-query')"></i>
                <h1 class="text-lg font-semibold">申报详情</h1>
            </div>

            <div class="px-4 py-6">
                <div class="bg-white rounded-lg p-4 shadow-sm mb-4">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="font-semibold text-gray-800">申报信息</h3>
                        <span id="detail-status" class="bg-warning text-white px-3 py-1 rounded-full text-sm">待审核</span>
                    </div>

                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">设备编号:</span>
                            <span class="font-medium" id="detail-equipment-id">EL001</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">设备类型:</span>
                            <span class="font-medium" id="detail-equipment-type">乘客电梯</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">使用单位:</span>
                            <span class="font-medium" id="detail-company">万达广场</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">申报时间:</span>
                            <span class="font-medium" id="detail-time">2024-01-15 10:30</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">联系人:</span>
                            <span class="font-medium" id="detail-contact">李经理</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">联系电话:</span>
                            <span class="font-medium" id="detail-phone">138****8888</span>
                        </div>
                    </div>
                </div>

                <!-- 进度跟踪 -->
                <div class="bg-white rounded-lg p-4 shadow-sm mb-4">
                    <h3 class="font-semibold text-gray-800 mb-4">进度跟踪</h3>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-secondary rounded-full mr-3"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium">申报提交</p>
                                <p class="text-xs text-gray-500">2024-01-15 10:30</p>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-warning rounded-full mr-3"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium">审核中</p>
                                <p class="text-xs text-gray-500">预计2个工作日内完成</p>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-gray-300 rounded-full mr-3"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-400">安排检验</p>
                                <p class="text-xs text-gray-400">待审核通过</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="space-y-3">
                    <button class="w-full bg-primary text-white py-3 rounded-lg font-medium">
                        <i class="fas fa-phone mr-2"></i>联系客服
                    </button>
                    <button class="w-full bg-gray-200 text-gray-700 py-3 rounded-lg font-medium">
                        <i class="fas fa-download mr-2"></i>下载申报单
                    </button>
                </div>
            </div>
        </div>

        <!-- 个人中心页面 -->
        <div id="profile" class="page hidden">
            <div class="bg-gradient-to-r from-primary to-blue-600 px-4 py-8 text-white">
                <div class="flex items-center">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face"
                         class="w-16 h-16 rounded-full mr-4" alt="头像">
                    <div>
                        <h2 class="text-xl font-bold">张三</h2>
                        <p class="text-blue-100">科大国创软件股份有限公司</p>
                        <p class="text-blue-100 text-sm">检验员 | ID: 001</p>
                    </div>
                </div>
            </div>

            <div class="px-4 py-6">
                <!-- 统计卡片 -->
                <div class="grid grid-cols-3 gap-4 mb-6">
                    <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                        <div class="text-2xl font-bold text-primary">15</div>
                        <div class="text-xs text-gray-600">本月申报</div>
                    </div>
                    <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                        <div class="text-2xl font-bold text-secondary">8</div>
                        <div class="text-xs text-gray-600">已完成</div>
                    </div>
                    <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                        <div class="text-2xl font-bold text-warning">3</div>
                        <div class="text-xs text-gray-600">待处理</div>
                    </div>
                </div>

                <!-- 功能菜单 -->
                <div class="bg-white rounded-lg shadow-sm mb-4">
                    <div class="p-4 border-b border-gray-100 flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-user-edit text-primary mr-3"></i>
                            <span class="font-medium">个人信息</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                    <div class="p-4 border-b border-gray-100 flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-key text-primary mr-3"></i>
                            <span class="font-medium">修改密码</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                    <div class="p-4 border-b border-gray-100 flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-bell text-primary mr-3"></i>
                            <span class="font-medium">消息通知</span>
                        </div>
                        <div class="flex items-center">
                            <span class="bg-danger text-white text-xs rounded-full w-5 h-5 flex items-center justify-center mr-2">99</span>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                    <div class="p-4 flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-cog text-primary mr-3"></i>
                            <span class="font-medium">系统设置</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm mb-4">
                    <div class="p-4 border-b border-gray-100 flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-question-circle text-primary mr-3"></i>
                            <span class="font-medium">帮助中心</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                    <div class="p-4 border-b border-gray-100 flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-info-circle text-primary mr-3"></i>
                            <span class="font-medium">关于我们</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                    <div class="p-4 flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-sign-out-alt text-danger mr-3"></i>
                            <span class="font-medium text-danger">退出登录</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div class="flex">
            <button class="flex-1 py-3 text-center" onclick="navigateTo('home')" id="nav-home">
                <i class="fas fa-home text-xl text-primary mb-1 block"></i>
                <span class="text-xs text-primary font-medium">首页</span>
            </button>
            <button class="flex-1 py-3 text-center" onclick="navigateTo('profile')" id="nav-profile">
                <i class="fas fa-user text-xl text-gray-400 mb-1 block"></i>
                <span class="text-xs text-gray-400">我的</span>
            </button>
        </div>
        <div class="h-1 bg-primary w-16 mx-auto"></div>
    </div>

    <script>
        // 页面导航功能
        function navigateTo(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.add('hidden');
            });

            // 显示目标页面
            document.getElementById(pageId).classList.remove('hidden');

            // 更新底部导航状态
            updateNavigation(pageId);
        }

        function updateNavigation(activePageId) {
            // 重置所有导航按钮
            document.querySelectorAll('[id^="nav-"]').forEach(nav => {
                const icon = nav.querySelector('i');
                const text = nav.querySelector('span');
                icon.className = icon.className.replace('text-primary', 'text-gray-400');
                text.className = text.className.replace('text-primary', 'text-gray-400');
            });

            // 激活当前页面对应的导航
            if (activePageId === 'home') {
                const homeNav = document.getElementById('nav-home');
                const icon = homeNav.querySelector('i');
                const text = homeNav.querySelector('span');
                icon.className = icon.className.replace('text-gray-400', 'text-primary');
                text.className = text.className.replace('text-gray-400', 'text-primary');
            } else if (activePageId === 'profile') {
                const profileNav = document.getElementById('nav-profile');
                const icon = profileNav.querySelector('i');
                const text = profileNav.querySelector('span');
                icon.className = icon.className.replace('text-gray-400', 'text-primary');
                text.className = text.className.replace('text-gray-400', 'text-primary');
            }
        }

        // 显示申报详情
        function showReportDetail(reportId) {
            // 这里可以根据reportId加载不同的详情数据
            navigateTo('report-detail');
        }

        // 提交检验申报
        function submitInspection() {
            // 模拟提交过程
            const button = event.target;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>提交中...';
            button.disabled = true;

            setTimeout(() => {
                alert('申报提交成功！');
                button.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>提交申报';
                button.disabled = false;
                navigateTo('home');
            }, 2000);
        }

        // 保存草稿
        function saveDraft() {
            alert('草稿已保存');
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示首页
            navigateTo('home');
        });
    </script>

    <style>
        /* 自定义样式 */
        .page {
            transition: all 0.3s ease-in-out;
        }

        .page.hidden {
            display: none !important;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }

        /* 输入框焦点效果 */
        input:focus, select:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 按钮点击效果 */
        button:active {
            transform: scale(0.98);
        }

        /* 卡片悬停效果 */
        .shadow-sm:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: box-shadow 0.2s ease-in-out;
        }

        /* 状态指示器动画 */
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }

        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        /* 确保页面内容不被底部导航遮挡 */
        body {
            padding-bottom: env(safe-area-inset-bottom);
        }

        /* 移动端适配 */
        @media (max-width: 640px) {
            .grid-cols-3 > div {
                min-height: 80px;
            }

            .grid-cols-2 > div {
                min-height: 60px;
            }
        }
    </style>
</body>
</html>
