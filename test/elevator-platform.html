<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电梯检测管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#2563EB',
                        'primary-light': '#3B82F6',
                        'primary-dark': '#1D4ED8',
                        secondary: '#10B981',
                        'secondary-light': '#34D399',
                        warning: '#F59E0B',
                        'warning-light': '#FBBF24',
                        danger: '#EF4444',
                        'danger-light': '#F87171',
                        'blue-gradient-start': '#667EEA',
                        'blue-gradient-end': '#764BA2',
                        'card-bg': '#FFFFFF',
                        'bg-light': '#F8FAFC'
                    },
                    boxShadow: {
                        'card': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                        'card-hover': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                        'button': '0 2px 4px rgba(37, 99, 235, 0.2)',
                        'button-hover': '0 4px 8px rgba(37, 99, 235, 0.3)'
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'bounce-gentle': 'bounceGentle 2s infinite'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-bg-light font-sans overflow-x-hidden">
    <!-- 状态栏 -->
    <div class="bg-gradient-to-r from-primary to-primary-light px-4 py-2 flex justify-between items-center text-sm font-medium text-white">
        <span class="font-semibold">12:00</span>
        <div class="flex items-center space-x-2">
            <i class="fas fa-signal text-white"></i>
            <i class="fas fa-wifi text-white"></i>
            <i class="fas fa-battery-full text-white"></i>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="min-h-screen pb-20">
        <!-- 首页 -->
        <div id="home" class="page active">
            <!-- 头部 -->
            <div class="bg-gradient-to-br from-primary via-primary-light to-blue-gradient-start px-4 py-6 relative overflow-hidden">
                <!-- 装饰性背景元素 -->
                <div class="absolute top-0 right-0 w-32 h-32 bg-white opacity-10 rounded-full -mr-16 -mt-16"></div>
                <div class="absolute bottom-0 left-0 w-24 h-24 bg-white opacity-5 rounded-full -ml-12 -mb-12"></div>

                <div class="flex justify-between items-center relative z-10">
                    <div class="animate-fade-in">
                        <h1 class="text-xl font-bold text-white mb-1">张三</h1>
                        <p class="text-blue-100 text-sm">科大国创软件股份有限公司</p>
                        <div class="flex items-center mt-2">
                            <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                            <span class="text-blue-100 text-xs">在线</span>
                        </div>
                    </div>
                    <div class="relative animate-bounce-gentle">
                        <div class="bg-white bg-opacity-20 p-3 rounded-full backdrop-blur-sm">
                            <i class="fas fa-bell text-xl text-white"></i>
                        </div>
                        <span class="absolute -top-1 -right-1 bg-danger text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold shadow-lg animate-pulse">99</span>
                    </div>
                </div>
            </div>

            <!-- 快捷办事 -->
            <div class="px-4 py-6 bg-gradient-to-b from-transparent to-bg-light">
                <div class="flex items-center mb-6 animate-slide-up">
                    <div class="w-1 h-8 bg-gradient-to-b from-primary to-primary-light rounded-full mr-4 shadow-sm"></div>
                    <h2 class="text-xl font-bold text-gray-800">快捷办事</h2>
                    <div class="ml-auto">
                        <div class="w-8 h-8 bg-gradient-to-r from-primary to-primary-light rounded-full flex items-center justify-center">
                            <i class="fas fa-bolt text-white text-sm"></i>
                        </div>
                    </div>
                </div>
                
                <div class="grid grid-cols-3 gap-4 mb-10">
                    <div class="bg-card-bg rounded-2xl p-4 shadow-card hover:shadow-card-hover text-center transform transition-all duration-300 hover:scale-105 hover:-translate-y-1 cursor-pointer group" onclick="navigateTo('inspection-report')">
                        <div class="w-14 h-14 bg-gradient-to-br from-blue-400 to-primary rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-button">
                            <i class="fas fa-clipboard-check text-white text-xl"></i>
                        </div>
                        <p class="text-sm text-gray-700 font-semibold group-hover:text-primary transition-colors duration-300">检验申报</p>
                    </div>

                    <div class="bg-card-bg rounded-2xl p-4 shadow-card hover:shadow-card-hover text-center transform transition-all duration-300 hover:scale-105 hover:-translate-y-1 cursor-pointer group" onclick="navigateTo('elevator-inspection')">
                        <div class="w-14 h-14 bg-gradient-to-br from-green-400 to-secondary rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-button">
                            <i class="fas fa-building text-white text-xl"></i>
                        </div>
                        <p class="text-sm text-gray-700 font-semibold group-hover:text-secondary transition-colors duration-300">电梯检测申报</p>
                    </div>

                    <div class="bg-card-bg rounded-2xl p-4 shadow-card hover:shadow-card-hover text-center transform transition-all duration-300 hover:scale-105 hover:-translate-y-1 cursor-pointer group" onclick="navigateTo('equipment-report')">
                        <div class="w-14 h-14 bg-gradient-to-br from-purple-400 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-button">
                            <i class="fas fa-tools text-white text-xl"></i>
                        </div>
                        <p class="text-sm text-gray-700 font-semibold group-hover:text-purple-600 transition-colors duration-300">无设备申报</p>
                    </div>

                    <div class="bg-card-bg rounded-2xl p-4 shadow-card hover:shadow-card-hover text-center transform transition-all duration-300 hover:scale-105 hover:-translate-y-1 cursor-pointer group" onclick="navigateTo('recheck-report')">
                        <div class="w-14 h-14 bg-gradient-to-br from-yellow-400 to-warning rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-button">
                            <i class="fas fa-redo text-white text-xl"></i>
                        </div>
                        <p class="text-sm text-gray-700 font-semibold group-hover:text-warning transition-colors duration-300">复检申报</p>
                    </div>

                    <div class="bg-card-bg rounded-2xl p-4 shadow-card hover:shadow-card-hover text-center transform transition-all duration-300 hover:scale-105 hover:-translate-y-1 cursor-pointer group" onclick="navigateTo('certificate-change')">
                        <div class="w-14 h-14 bg-gradient-to-br from-orange-400 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-button">
                            <i class="fas fa-exchange-alt text-white text-xl"></i>
                        </div>
                        <p class="text-sm text-gray-700 font-semibold group-hover:text-orange-600 transition-colors duration-300">合格证换发</p>
                    </div>

                    <div class="bg-card-bg rounded-2xl p-4 shadow-card hover:shadow-card-hover text-center transform transition-all duration-300 hover:scale-105 hover:-translate-y-1 cursor-pointer group" onclick="navigateTo('certificate-modify')">
                        <div class="w-14 h-14 bg-gradient-to-br from-red-400 to-danger rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-button">
                            <i class="fas fa-edit text-white text-xl"></i>
                        </div>
                        <p class="text-sm text-gray-700 font-semibold group-hover:text-danger transition-colors duration-300">合格证修改</p>
                    </div>
                </div>

                <!-- 综合查询 -->
                <div class="flex items-center mb-6 animate-slide-up">
                    <div class="w-1 h-8 bg-gradient-to-b from-secondary to-green-600 rounded-full mr-4 shadow-sm"></div>
                    <h2 class="text-xl font-bold text-gray-800">综合查询</h2>
                    <div class="ml-auto">
                        <div class="w-8 h-8 bg-gradient-to-r from-secondary to-green-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-search text-white text-sm"></i>
                        </div>
                    </div>
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-card-bg rounded-2xl p-4 shadow-card hover:shadow-card-hover transform transition-all duration-300 hover:scale-105 cursor-pointer group" onclick="navigateTo('report-query')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-primary rounded-xl flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-search text-white text-lg"></i>
                                </div>
                                <span class="text-sm font-semibold text-gray-700 group-hover:text-primary transition-colors duration-300">申报查询</span>
                            </div>
                            <span class="bg-gradient-to-r from-danger to-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold shadow-lg animate-pulse">5</span>
                        </div>
                    </div>

                    <div class="bg-card-bg rounded-2xl p-4 shadow-card hover:shadow-card-hover transform transition-all duration-300 hover:scale-105 cursor-pointer group" onclick="navigateTo('report-inquiry')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-secondary rounded-xl flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-file-alt text-white text-lg"></i>
                                </div>
                                <span class="text-sm font-semibold text-gray-700 group-hover:text-secondary transition-colors duration-300">报告查询</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-card-bg rounded-2xl p-4 shadow-card hover:shadow-card-hover transform transition-all duration-300 hover:scale-105 cursor-pointer group" onclick="navigateTo('qualification-query')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-xl flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-certificate text-white text-lg"></i>
                                </div>
                                <span class="text-sm font-semibold text-gray-700 group-hover:text-purple-600 transition-colors duration-300">合格证查询</span>
                            </div>
                            <span class="bg-gradient-to-r from-warning to-yellow-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold shadow-lg animate-pulse">5</span>
                        </div>
                    </div>

                    <div class="bg-card-bg rounded-2xl p-4 shadow-card hover:shadow-card-hover transform transition-all duration-300 hover:scale-105 cursor-pointer group" onclick="navigateTo('notice-query')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-xl flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-bell text-white text-lg"></i>
                                </div>
                                <span class="text-sm font-semibold text-gray-700 group-hover:text-orange-600 transition-colors duration-300">意见书查询</span>
                            </div>
                            <span class="bg-gradient-to-r from-secondary to-green-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold shadow-lg animate-pulse">3</span>
                        </div>
                    </div>

                    <div class="bg-card-bg rounded-2xl p-4 shadow-card hover:shadow-card-hover transform transition-all duration-300 hover:scale-105 cursor-pointer group" onclick="navigateTo('contact-query')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-gradient-to-br from-indigo-400 to-indigo-600 rounded-xl flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-address-book text-white text-lg"></i>
                                </div>
                                <span class="text-sm font-semibold text-gray-700 group-hover:text-indigo-600 transition-colors duration-300">联络查询</span>
                            </div>
                            <span class="bg-gradient-to-r from-pink-400 to-pink-600 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold shadow-lg animate-pulse">1</span>
                        </div>
                    </div>

                    <div class="bg-card-bg rounded-2xl p-4 shadow-card hover:shadow-card-hover transform transition-all duration-300 hover:scale-105 cursor-pointer group" onclick="navigateTo('equipment-query')">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gradient-to-br from-gray-400 to-gray-600 rounded-xl flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-cogs text-white text-lg"></i>
                            </div>
                            <span class="text-sm font-semibold text-gray-700 group-hover:text-gray-600 transition-colors duration-300">设备查询</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 检验申报页面 -->
        <div id="inspection-report" class="page hidden">
            <div class="bg-gradient-to-r from-primary to-primary-light px-4 py-4 shadow-lg flex items-center">
                <div class="bg-white bg-opacity-20 p-2 rounded-full mr-4 cursor-pointer hover:bg-opacity-30 transition-all duration-300" onclick="navigateTo('home')">
                    <i class="fas fa-arrow-left text-xl text-white"></i>
                </div>
                <h1 class="text-xl font-bold text-white">检验申报</h1>
            </div>

            <div class="px-4 py-6 bg-bg-light">
                <div class="bg-card-bg rounded-2xl p-6 shadow-card mb-6 animate-slide-up">
                    <div class="flex items-center mb-6">
                        <div class="w-8 h-8 bg-gradient-to-r from-primary to-primary-light rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-edit text-white text-sm"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-800">申报信息</h3>
                    </div>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-3">设备编号</label>
                            <input type="text" class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 bg-gray-50 focus:bg-white" placeholder="请输入设备编号">
                        </div>

                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-3">设备类型</label>
                            <select class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 bg-gray-50 focus:bg-white">
                                <option>请选择设备类型</option>
                                <option>乘客电梯</option>
                                <option>载货电梯</option>
                                <option>自动扶梯</option>
                                <option>自动人行道</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-3">使用单位</label>
                            <input type="text" class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 bg-gray-50 focus:bg-white" placeholder="请输入使用单位">
                        </div>

                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-3">联系人</label>
                            <input type="text" class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 bg-gray-50 focus:bg-white" placeholder="请输入联系人">
                        </div>

                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-3">联系电话</label>
                            <input type="tel" class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 bg-gray-50 focus:bg-white" placeholder="请输入联系电话">
                        </div>

                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-3">预约检验时间</label>
                            <input type="date" class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 bg-gray-50 focus:bg-white">
                        </div>
                    </div>

                    <div class="mt-8 space-y-4">
                        <button class="w-full bg-gradient-to-r from-primary to-primary-light text-white py-4 rounded-xl font-semibold shadow-button hover:shadow-button-hover transform hover:scale-105 transition-all duration-300" onclick="submitInspection()">
                            <i class="fas fa-paper-plane mr-2"></i>提交申报
                        </button>
                        <button class="w-full bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 py-4 rounded-xl font-semibold hover:from-gray-200 hover:to-gray-300 transform hover:scale-105 transition-all duration-300" onclick="saveDraft()">
                            <i class="fas fa-save mr-2"></i>保存草稿
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 申报查询页面 -->
        <div id="report-query" class="page hidden">
            <div class="bg-gradient-to-r from-secondary to-green-600 px-4 py-4 shadow-lg flex items-center">
                <div class="bg-white bg-opacity-20 p-2 rounded-full mr-4 cursor-pointer hover:bg-opacity-30 transition-all duration-300" onclick="navigateTo('home')">
                    <i class="fas fa-arrow-left text-xl text-white"></i>
                </div>
                <h1 class="text-xl font-bold text-white">申报查询</h1>
            </div>

            <div class="px-4 py-4">
                <!-- 搜索栏 -->
                <div class="bg-card-bg rounded-2xl p-5 shadow-card mb-6 animate-slide-up">
                    <div class="flex space-x-3">
                        <input type="text" class="flex-1 px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent transition-all duration-300 bg-gray-50 focus:bg-white" placeholder="输入设备编号或使用单位">
                        <button class="bg-gradient-to-r from-secondary to-green-600 text-white px-6 py-3 rounded-xl shadow-button hover:shadow-button-hover transform hover:scale-105 transition-all duration-300">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- 筛选标签 -->
                <div class="flex space-x-3 mb-6 overflow-x-auto pb-2">
                    <button class="bg-gradient-to-r from-primary to-primary-light text-white px-5 py-2 rounded-full text-sm whitespace-nowrap font-semibold shadow-lg">全部</button>
                    <button class="bg-white text-gray-700 px-5 py-2 rounded-full text-sm whitespace-nowrap font-medium border-2 border-gray-200 hover:border-warning hover:text-warning transition-all duration-300">待审核</button>
                    <button class="bg-white text-gray-700 px-5 py-2 rounded-full text-sm whitespace-nowrap font-medium border-2 border-gray-200 hover:border-secondary hover:text-secondary transition-all duration-300">已通过</button>
                    <button class="bg-white text-gray-700 px-5 py-2 rounded-full text-sm whitespace-nowrap font-medium border-2 border-gray-200 hover:border-danger hover:text-danger transition-all duration-300">已拒绝</button>
                </div>

                <!-- 申报列表 -->
                <div class="space-y-4">
                    <div class="bg-card-bg rounded-2xl p-5 shadow-card hover:shadow-card-hover transform hover:scale-105 transition-all duration-300 cursor-pointer group" onclick="showReportDetail('001')">
                        <div class="flex justify-between items-start mb-3">
                            <h4 class="font-bold text-gray-800 group-hover:text-primary transition-colors duration-300">设备编号: EL001</h4>
                            <span class="bg-gradient-to-r from-warning to-yellow-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg animate-pulse">待审核</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2 flex items-center">
                            <i class="fas fa-building text-gray-400 mr-2"></i>
                            使用单位: 万达广场
                        </p>
                        <p class="text-sm text-gray-600 mb-3 flex items-center">
                            <i class="fas fa-clock text-gray-400 mr-2"></i>
                            申报时间: 2024-01-15 10:30
                        </p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-500 bg-gray-100 px-3 py-1 rounded-full font-medium">乘客电梯</span>
                            <i class="fas fa-chevron-right text-gray-400 group-hover:text-primary transition-colors duration-300"></i>
                        </div>
                    </div>

                    <div class="bg-card-bg rounded-2xl p-5 shadow-card hover:shadow-card-hover transform hover:scale-105 transition-all duration-300 cursor-pointer group" onclick="showReportDetail('002')">
                        <div class="flex justify-between items-start mb-3">
                            <h4 class="font-bold text-gray-800 group-hover:text-secondary transition-colors duration-300">设备编号: EL002</h4>
                            <span class="bg-gradient-to-r from-secondary to-green-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg">已通过</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2 flex items-center">
                            <i class="fas fa-building text-gray-400 mr-2"></i>
                            使用单位: 银泰中心
                        </p>
                        <p class="text-sm text-gray-600 mb-3 flex items-center">
                            <i class="fas fa-clock text-gray-400 mr-2"></i>
                            申报时间: 2024-01-14 14:20
                        </p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-500 bg-gray-100 px-3 py-1 rounded-full font-medium">载货电梯</span>
                            <i class="fas fa-chevron-right text-gray-400 group-hover:text-secondary transition-colors duration-300"></i>
                        </div>
                    </div>

                    <div class="bg-card-bg rounded-2xl p-5 shadow-card hover:shadow-card-hover transform hover:scale-105 transition-all duration-300 cursor-pointer group" onclick="showReportDetail('003')">
                        <div class="flex justify-between items-start mb-3">
                            <h4 class="font-bold text-gray-800 group-hover:text-danger transition-colors duration-300">设备编号: EL003</h4>
                            <span class="bg-gradient-to-r from-danger to-red-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg">已拒绝</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2 flex items-center">
                            <i class="fas fa-building text-gray-400 mr-2"></i>
                            使用单位: 恒隆广场
                        </p>
                        <p class="text-sm text-gray-600 mb-3 flex items-center">
                            <i class="fas fa-clock text-gray-400 mr-2"></i>
                            申报时间: 2024-01-13 09:15
                        </p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-500 bg-gray-100 px-3 py-1 rounded-full font-medium">自动扶梯</span>
                            <i class="fas fa-chevron-right text-gray-400 group-hover:text-danger transition-colors duration-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 报告详情页面 -->
        <div id="report-detail" class="page hidden">
            <div class="bg-white px-4 py-4 shadow-sm flex items-center">
                <i class="fas fa-arrow-left text-xl text-gray-600 mr-4" onclick="navigateTo('report-query')"></i>
                <h1 class="text-lg font-semibold">申报详情</h1>
            </div>

            <div class="px-4 py-6">
                <div class="bg-white rounded-lg p-4 shadow-sm mb-4">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="font-semibold text-gray-800">申报信息</h3>
                        <span id="detail-status" class="bg-warning text-white px-3 py-1 rounded-full text-sm">待审核</span>
                    </div>

                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">设备编号:</span>
                            <span class="font-medium" id="detail-equipment-id">EL001</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">设备类型:</span>
                            <span class="font-medium" id="detail-equipment-type">乘客电梯</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">使用单位:</span>
                            <span class="font-medium" id="detail-company">万达广场</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">申报时间:</span>
                            <span class="font-medium" id="detail-time">2024-01-15 10:30</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">联系人:</span>
                            <span class="font-medium" id="detail-contact">李经理</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">联系电话:</span>
                            <span class="font-medium" id="detail-phone">138****8888</span>
                        </div>
                    </div>
                </div>

                <!-- 进度跟踪 -->
                <div class="bg-white rounded-lg p-4 shadow-sm mb-4">
                    <h3 class="font-semibold text-gray-800 mb-4">进度跟踪</h3>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-secondary rounded-full mr-3"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium">申报提交</p>
                                <p class="text-xs text-gray-500">2024-01-15 10:30</p>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-warning rounded-full mr-3"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium">审核中</p>
                                <p class="text-xs text-gray-500">预计2个工作日内完成</p>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-gray-300 rounded-full mr-3"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-400">安排检验</p>
                                <p class="text-xs text-gray-400">待审核通过</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="space-y-3">
                    <button class="w-full bg-primary text-white py-3 rounded-lg font-medium">
                        <i class="fas fa-phone mr-2"></i>联系客服
                    </button>
                    <button class="w-full bg-gray-200 text-gray-700 py-3 rounded-lg font-medium">
                        <i class="fas fa-download mr-2"></i>下载申报单
                    </button>
                </div>
            </div>
        </div>

        <!-- 个人中心页面 -->
        <div id="profile" class="page hidden">
            <div class="bg-gradient-to-br from-blue-gradient-start via-primary to-blue-gradient-end px-4 py-10 text-white relative overflow-hidden">
                <!-- 装饰性背景元素 -->
                <div class="absolute top-0 right-0 w-40 h-40 bg-white opacity-10 rounded-full -mr-20 -mt-20"></div>
                <div class="absolute bottom-0 left-0 w-32 h-32 bg-white opacity-5 rounded-full -ml-16 -mb-16"></div>
                <div class="absolute top-1/2 left-1/2 w-24 h-24 bg-white opacity-5 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>

                <div class="flex items-center relative z-10 animate-fade-in">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face"
                             class="w-20 h-20 rounded-full mr-5 border-4 border-white border-opacity-30 shadow-lg" alt="头像">
                        <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold mb-1">张三</h2>
                        <p class="text-blue-100 text-sm mb-1">科大国创软件股份有限公司</p>
                        <div class="flex items-center">
                            <span class="text-blue-100 text-sm mr-3">检验员</span>
                            <span class="bg-white bg-opacity-20 text-white text-xs px-2 py-1 rounded-full font-medium">ID: 001</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="px-4 py-6 bg-bg-light">
                <!-- 统计卡片 -->
                <div class="grid grid-cols-3 gap-4 mb-8 -mt-6 relative z-10">
                    <div class="bg-card-bg rounded-2xl p-5 text-center shadow-card hover:shadow-card-hover transform hover:scale-105 transition-all duration-300 group">
                        <div class="w-12 h-12 bg-gradient-to-br from-primary to-primary-light rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-file-alt text-white text-lg"></i>
                        </div>
                        <div class="text-2xl font-bold text-primary mb-1">15</div>
                        <div class="text-xs text-gray-600 font-medium">本月申报</div>
                    </div>
                    <div class="bg-card-bg rounded-2xl p-5 text-center shadow-card hover:shadow-card-hover transform hover:scale-105 transition-all duration-300 group">
                        <div class="w-12 h-12 bg-gradient-to-br from-secondary to-green-600 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-check-circle text-white text-lg"></i>
                        </div>
                        <div class="text-2xl font-bold text-secondary mb-1">8</div>
                        <div class="text-xs text-gray-600 font-medium">已完成</div>
                    </div>
                    <div class="bg-card-bg rounded-2xl p-5 text-center shadow-card hover:shadow-card-hover transform hover:scale-105 transition-all duration-300 group">
                        <div class="w-12 h-12 bg-gradient-to-br from-warning to-yellow-500 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-clock text-white text-lg"></i>
                        </div>
                        <div class="text-2xl font-bold text-warning mb-1">3</div>
                        <div class="text-xs text-gray-600 font-medium">待处理</div>
                    </div>
                </div>

                <!-- 功能菜单 -->
                <div class="bg-white rounded-lg shadow-sm mb-4">
                    <div class="p-4 border-b border-gray-100 flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-user-edit text-primary mr-3"></i>
                            <span class="font-medium">个人信息</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                    <div class="p-4 border-b border-gray-100 flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-key text-primary mr-3"></i>
                            <span class="font-medium">修改密码</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                    <div class="p-4 border-b border-gray-100 flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-bell text-primary mr-3"></i>
                            <span class="font-medium">消息通知</span>
                        </div>
                        <div class="flex items-center">
                            <span class="bg-danger text-white text-xs rounded-full w-5 h-5 flex items-center justify-center mr-2">99</span>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                    <div class="p-4 flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-cog text-primary mr-3"></i>
                            <span class="font-medium">系统设置</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm mb-4">
                    <div class="p-4 border-b border-gray-100 flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-question-circle text-primary mr-3"></i>
                            <span class="font-medium">帮助中心</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                    <div class="p-4 border-b border-gray-100 flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-info-circle text-primary mr-3"></i>
                            <span class="font-medium">关于我们</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                    <div class="p-4 flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-sign-out-alt text-danger mr-3"></i>
                            <span class="font-medium text-danger">退出登录</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 shadow-lg backdrop-blur-sm bg-opacity-95">
        <div class="flex">
            <button class="flex-1 py-4 text-center relative group" onclick="navigateTo('home')" id="nav-home">
                <div class="absolute inset-0 bg-gradient-to-r from-primary to-primary-light opacity-0 group-hover:opacity-10 rounded-t-2xl transition-opacity duration-300"></div>
                <i class="fas fa-home text-2xl text-primary mb-1 block relative z-10 transform group-hover:scale-110 transition-transform duration-300"></i>
                <span class="text-xs text-primary font-semibold relative z-10">首页</span>
            </button>
            <button class="flex-1 py-4 text-center relative group" onclick="navigateTo('profile')" id="nav-profile">
                <div class="absolute inset-0 bg-gradient-to-r from-gray-400 to-gray-500 opacity-0 group-hover:opacity-10 rounded-t-2xl transition-opacity duration-300"></div>
                <i class="fas fa-user text-2xl text-gray-400 mb-1 block relative z-10 transform group-hover:scale-110 transition-transform duration-300"></i>
                <span class="text-xs text-gray-400 font-medium relative z-10">我的</span>
            </button>
        </div>
        <div class="h-1 bg-gradient-to-r from-primary to-primary-light w-20 mx-auto rounded-full"></div>
    </div>

    <script>
        // 页面导航功能
        function navigateTo(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.add('hidden');
            });

            // 显示目标页面
            document.getElementById(pageId).classList.remove('hidden');

            // 更新底部导航状态
            updateNavigation(pageId);
        }

        function updateNavigation(activePageId) {
            // 重置所有导航按钮
            document.querySelectorAll('[id^="nav-"]').forEach(nav => {
                const icon = nav.querySelector('i');
                const text = nav.querySelector('span');
                icon.className = icon.className.replace('text-primary', 'text-gray-400');
                text.className = text.className.replace('text-primary', 'text-gray-400');
            });

            // 激活当前页面对应的导航
            if (activePageId === 'home') {
                const homeNav = document.getElementById('nav-home');
                const icon = homeNav.querySelector('i');
                const text = homeNav.querySelector('span');
                icon.className = icon.className.replace('text-gray-400', 'text-primary');
                text.className = text.className.replace('text-gray-400', 'text-primary');
            } else if (activePageId === 'profile') {
                const profileNav = document.getElementById('nav-profile');
                const icon = profileNav.querySelector('i');
                const text = profileNav.querySelector('span');
                icon.className = icon.className.replace('text-gray-400', 'text-primary');
                text.className = text.className.replace('text-gray-400', 'text-primary');
            }
        }

        // 显示申报详情
        function showReportDetail(reportId) {
            // 这里可以根据reportId加载不同的详情数据
            navigateTo('report-detail');
        }

        // 提交检验申报
        function submitInspection() {
            // 模拟提交过程
            const button = event.target;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>提交中...';
            button.disabled = true;

            setTimeout(() => {
                alert('申报提交成功！');
                button.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>提交申报';
                button.disabled = false;
                navigateTo('home');
            }, 2000);
        }

        // 保存草稿
        function saveDraft() {
            alert('草稿已保存');
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示首页
            navigateTo('home');
        });
    </script>

    <style>
        /* 自定义样式 */
        .page {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .page.hidden {
            display: none !important;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }

        /* 自定义动画 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounceGentle {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-5px);
            }
        }

        @keyframes shimmer {
            0% {
                background-position: -200px 0;
            }
            100% {
                background-position: calc(200px + 100%) 0;
            }
        }

        /* 输入框焦点效果 */
        input:focus, select:focus {
            box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
            transform: translateY(-1px);
        }

        /* 按钮点击效果 */
        button:active {
            transform: scale(0.95);
        }

        /* 渐变文字效果 */
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 玻璃态效果 */
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        /* 悬浮动画 */
        .float-animation {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        /* 脉冲效果增强 */
        .pulse-enhanced {
            animation: pulseEnhanced 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        @keyframes pulseEnhanced {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.05);
            }
        }

        /* 确保页面内容不被底部导航遮挡 */
        body {
            padding-bottom: env(safe-area-inset-bottom);
        }

        /* 移动端适配 */
        @media (max-width: 640px) {
            .grid-cols-3 > div {
                min-height: 90px;
            }

            .grid-cols-2 > div {
                min-height: 70px;
            }
        }

        /* 响应式字体 */
        @media (max-width: 375px) {
            .text-xl {
                font-size: 1.125rem;
            }

            .text-2xl {
                font-size: 1.5rem;
            }
        }
    </style>
</body>
</html>
