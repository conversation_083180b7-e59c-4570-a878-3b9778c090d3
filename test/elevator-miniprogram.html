<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>电梯检测管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary-blue': '#1E40AF',
                        'light-blue': '#3B82F6',
                        'soft-blue': '#60A5FA',
                        'pale-blue': '#DBEAFE',
                        'dark-blue': '#1E3A8A',
                        'accent-blue': '#2563EB'
                    },
                    fontFamily: {
                        'sans': ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Display', 'Helvetica Neue', 'Arial', 'sans-serif']
                    },
                    spacing: {
                        '18': '4.5rem',
                        '88': '22rem',
                        '128': '32rem'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-sans overflow-x-hidden">
    <!-- iPhone 16 Pro Max 状态栏 -->
    <div class="bg-primary-blue text-white px-6 py-3 flex justify-between items-center text-sm font-medium">
        <span class="font-semibold">9:41</span>
        <div class="flex items-center space-x-2">
            <i class="fas fa-signal text-white"></i>
            <i class="fas fa-wifi text-white"></i>
            <span class="text-xs">100%</span>
            <i class="fas fa-battery-full text-white"></i>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="min-h-screen bg-gradient-to-br from-blue-50 to-white">
        <!-- 首页 -->
        <div id="home" class="page">
            <!-- 头部用户信息区域 -->
            <div class="bg-gradient-to-br from-primary-blue via-accent-blue to-light-blue px-6 py-8 relative overflow-hidden">
                <!-- 装饰性背景 -->
                <div class="absolute top-0 right-0 w-32 h-32 bg-white opacity-10 rounded-full -mr-16 -mt-16"></div>
                <div class="absolute bottom-0 left-0 w-24 h-24 bg-white opacity-5 rounded-full -ml-12 -mb-12"></div>
                
                <div class="flex items-center justify-between relative z-10">
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face" 
                             class="w-16 h-16 rounded-full border-3 border-white border-opacity-30 shadow-lg mr-4" alt="用户头像">
                        <div>
                            <h1 class="text-xl font-bold text-white mb-1">张工程师</h1>
                            <p class="text-blue-100 text-sm">科大国创软件股份有限公司</p>
                            <div class="flex items-center mt-2">
                                <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                                <span class="text-blue-100 text-xs">在线</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 消息通知 -->
                    <div class="relative" onclick="showNotifications()">
                        <div class="bg-white bg-opacity-20 p-3 rounded-full backdrop-blur-sm cursor-pointer hover:bg-opacity-30 transition-all duration-300">
                            <i class="fas fa-bell text-xl text-white"></i>
                        </div>
                        <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold shadow-lg animate-pulse">8</span>
                    </div>
                </div>
            </div>

            <!-- 快捷办事区域 -->
            <div class="px-6 py-6">
                <div class="flex items-center mb-6">
                    <div class="w-1 h-8 bg-gradient-to-b from-primary-blue to-light-blue rounded-full mr-4"></div>
                    <h2 class="text-xl font-bold text-gray-800">快捷办事</h2>
                    <div class="ml-auto">
                        <div class="w-8 h-8 bg-gradient-to-r from-primary-blue to-light-blue rounded-full flex items-center justify-center">
                            <i class="fas fa-bolt text-white text-sm"></i>
                        </div>
                    </div>
                </div>
                
                <div class="grid grid-cols-3 gap-4 mb-8">
                    <!-- 检验申报 -->
                    <div class="bg-white rounded-2xl p-4 shadow-lg hover:shadow-xl text-center transform transition-all duration-300 hover:scale-105 cursor-pointer group" onclick="navigateToPage('inspection-report')">
                        <div class="w-14 h-14 bg-gradient-to-br from-blue-400 to-primary-blue rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-clipboard-check text-white text-xl"></i>
                        </div>
                        <p class="text-sm text-gray-700 font-semibold group-hover:text-primary-blue transition-colors duration-300">检验申报</p>
                    </div>
                    
                    <!-- 电梯检测申报 -->
                    <div class="bg-white rounded-2xl p-4 shadow-lg hover:shadow-xl text-center transform transition-all duration-300 hover:scale-105 cursor-pointer group" onclick="navigateToPage('elevator-inspection')">
                        <div class="w-14 h-14 bg-gradient-to-br from-light-blue to-accent-blue rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-building text-white text-xl"></i>
                        </div>
                        <p class="text-sm text-gray-700 font-semibold group-hover:text-light-blue transition-colors duration-300">电梯检测申报</p>
                    </div>
                    
                    <!-- 无设备申报 -->
                    <div class="bg-white rounded-2xl p-4 shadow-lg hover:shadow-xl text-center transform transition-all duration-300 hover:scale-105 cursor-pointer group" onclick="navigateToPage('no-equipment')">
                        <div class="w-14 h-14 bg-gradient-to-br from-soft-blue to-light-blue rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-tools text-white text-xl"></i>
                        </div>
                        <p class="text-sm text-gray-700 font-semibold group-hover:text-soft-blue transition-colors duration-300">无设备申报</p>
                    </div>
                    
                    <!-- 复检申报 -->
                    <div class="bg-white rounded-2xl p-4 shadow-lg hover:shadow-xl text-center transform transition-all duration-300 hover:scale-105 cursor-pointer group" onclick="navigateToPage('recheck-report')">
                        <div class="w-14 h-14 bg-gradient-to-br from-primary-blue to-dark-blue rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-redo text-white text-xl"></i>
                        </div>
                        <p class="text-sm text-gray-700 font-semibold group-hover:text-primary-blue transition-colors duration-300">复检申报</p>
                    </div>
                    
                    <!-- 合格证换发 -->
                    <div class="bg-white rounded-2xl p-4 shadow-lg hover:shadow-xl text-center transform transition-all duration-300 hover:scale-105 cursor-pointer group" onclick="navigateToPage('certificate-change')">
                        <div class="w-14 h-14 bg-gradient-to-br from-accent-blue to-primary-blue rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-exchange-alt text-white text-xl"></i>
                        </div>
                        <p class="text-sm text-gray-700 font-semibold group-hover:text-accent-blue transition-colors duration-300">合格证换发</p>
                    </div>
                    
                    <!-- 合格证修改 -->
                    <div class="bg-white rounded-2xl p-4 shadow-lg hover:shadow-xl text-center transform transition-all duration-300 hover:scale-105 cursor-pointer group" onclick="navigateToPage('certificate-modify')">
                        <div class="w-14 h-14 bg-gradient-to-br from-light-blue to-soft-blue rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-edit text-white text-xl"></i>
                        </div>
                        <p class="text-sm text-gray-700 font-semibold group-hover:text-light-blue transition-colors duration-300">合格证修改</p>
                    </div>
                </div>

                <!-- 综合查询区域 -->
                <div class="flex items-center mb-6">
                    <div class="w-1 h-8 bg-gradient-to-b from-light-blue to-soft-blue rounded-full mr-4"></div>
                    <h2 class="text-xl font-bold text-gray-800">综合查询</h2>
                    <div class="ml-auto">
                        <div class="w-8 h-8 bg-gradient-to-r from-light-blue to-soft-blue rounded-full flex items-center justify-center">
                            <i class="fas fa-search text-white text-sm"></i>
                        </div>
                    </div>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-8">
                    <!-- 申报单查询 -->
                    <div class="bg-white rounded-2xl p-5 shadow-lg hover:shadow-xl transform transition-all duration-300 hover:scale-105 cursor-pointer group" onclick="navigateToPage('report-query')">
                        <div class="flex items-center justify-between mb-3">
                            <div class="w-12 h-12 bg-gradient-to-br from-primary-blue to-light-blue rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-file-alt text-white text-lg"></i>
                            </div>
                            <span class="bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold animate-pulse">3</span>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-700 group-hover:text-primary-blue transition-colors duration-300">申报单查询</h3>
                        <p class="text-xs text-gray-500 mt-1">查看申报进度</p>
                    </div>
                    
                    <!-- 报告查询 -->
                    <div class="bg-white rounded-2xl p-5 shadow-lg hover:shadow-xl transform transition-all duration-300 hover:scale-105 cursor-pointer group" onclick="navigateToPage('report-inquiry')">
                        <div class="flex items-center justify-between mb-3">
                            <div class="w-12 h-12 bg-gradient-to-br from-light-blue to-soft-blue rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-chart-line text-white text-lg"></i>
                            </div>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-700 group-hover:text-light-blue transition-colors duration-300">报告查询</h3>
                        <p class="text-xs text-gray-500 mt-1">检测报告下载</p>
                    </div>
                    
                    <!-- 合格证查询 -->
                    <div class="bg-white rounded-2xl p-5 shadow-lg hover:shadow-xl transform transition-all duration-300 hover:scale-105 cursor-pointer group" onclick="navigateToPage('certificate-query')">
                        <div class="flex items-center justify-between mb-3">
                            <div class="w-12 h-12 bg-gradient-to-br from-accent-blue to-primary-blue rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-certificate text-white text-lg"></i>
                            </div>
                            <span class="bg-blue-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold animate-pulse">2</span>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-700 group-hover:text-accent-blue transition-colors duration-300">合格证查询</h3>
                        <p class="text-xs text-gray-500 mt-1">合格证管理</p>
                    </div>
                    
                    <!-- 意见书查询 -->
                    <div class="bg-white rounded-2xl p-5 shadow-lg hover:shadow-xl transform transition-all duration-300 hover:scale-105 cursor-pointer group" onclick="navigateToPage('opinion-query')">
                        <div class="flex items-center justify-between mb-3">
                            <div class="w-12 h-12 bg-gradient-to-br from-soft-blue to-light-blue rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-comment-alt text-white text-lg"></i>
                            </div>
                            <span class="bg-orange-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold animate-pulse">1</span>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-700 group-hover:text-soft-blue transition-colors duration-300">意见书查询</h3>
                        <p class="text-xs text-gray-500 mt-1">整改意见查看</p>
                    </div>
                    
                    <!-- 联络单查询 -->
                    <div class="bg-white rounded-2xl p-5 shadow-lg hover:shadow-xl transform transition-all duration-300 hover:scale-105 cursor-pointer group" onclick="navigateToPage('contact-query')">
                        <div class="flex items-center justify-between mb-3">
                            <div class="w-12 h-12 bg-gradient-to-br from-primary-blue to-dark-blue rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-address-book text-white text-lg"></i>
                            </div>
                            <span class="bg-green-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold animate-pulse">2</span>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-700 group-hover:text-primary-blue transition-colors duration-300">联络单查询</h3>
                        <p class="text-xs text-gray-500 mt-1">联络信息管理</p>
                    </div>
                    
                    <!-- 设备查询 -->
                    <div class="bg-white rounded-2xl p-5 shadow-lg hover:shadow-xl transform transition-all duration-300 hover:scale-105 cursor-pointer group" onclick="navigateToPage('equipment-query')">
                        <div class="flex items-center justify-between mb-3">
                            <div class="w-12 h-12 bg-gradient-to-br from-light-blue to-accent-blue rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-cogs text-white text-lg"></i>
                            </div>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-700 group-hover:text-light-blue transition-colors duration-300">设备查询</h3>
                        <p class="text-xs text-gray-500 mt-1">设备信息查看</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 检验申报页面 -->
        <div id="inspection-report" class="page hidden">
            <div class="bg-gradient-to-r from-primary-blue to-light-blue px-6 py-4 flex items-center shadow-lg">
                <div class="bg-white bg-opacity-20 p-2 rounded-full mr-4 cursor-pointer hover:bg-opacity-30 transition-all duration-300" onclick="navigateToPage('home')">
                    <i class="fas fa-arrow-left text-xl text-white"></i>
                </div>
                <h1 class="text-xl font-bold text-white">检验申报</h1>
            </div>

            <div class="px-6 py-6 bg-gray-50 min-h-screen">
                <div class="bg-white rounded-2xl p-6 shadow-lg mb-6">
                    <div class="flex items-center mb-6">
                        <div class="w-8 h-8 bg-gradient-to-r from-primary-blue to-light-blue rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-edit text-white text-sm"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-800">申报信息</h3>
                    </div>

                    <div class="space-y-5">
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-3">设备编号</label>
                            <input type="text" class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-300 bg-gray-50 focus:bg-white" placeholder="请输入设备编号">
                        </div>

                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-3">设备类型</label>
                            <select class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-300 bg-gray-50 focus:bg-white">
                                <option>请选择设备类型</option>
                                <option>乘客电梯</option>
                                <option>载货电梯</option>
                                <option>自动扶梯</option>
                                <option>自动人行道</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-3">使用单位</label>
                            <input type="text" class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-300 bg-gray-50 focus:bg-white" placeholder="请输入使用单位">
                        </div>

                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-3">联系人</label>
                            <input type="text" class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-300 bg-gray-50 focus:bg-white" placeholder="请输入联系人">
                        </div>

                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-3">联系电话</label>
                            <input type="tel" class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-300 bg-gray-50 focus:bg-white" placeholder="请输入联系电话">
                        </div>

                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-3">预约检验时间</label>
                            <input type="date" class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-300 bg-gray-50 focus:bg-white">
                        </div>
                    </div>

                    <div class="mt-8 space-y-4">
                        <button class="w-full bg-gradient-to-r from-primary-blue to-light-blue text-white py-4 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300" onclick="submitInspection()">
                            <i class="fas fa-paper-plane mr-2"></i>提交申报
                        </button>
                        <button class="w-full bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 py-4 rounded-xl font-semibold hover:from-gray-200 hover:to-gray-300 transform hover:scale-105 transition-all duration-300" onclick="saveDraft()">
                            <i class="fas fa-save mr-2"></i>保存草稿
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 申报单查询页面 -->
        <div id="report-query" class="page hidden">
            <div class="bg-gradient-to-r from-light-blue to-soft-blue px-6 py-4 flex items-center shadow-lg">
                <div class="bg-white bg-opacity-20 p-2 rounded-full mr-4 cursor-pointer hover:bg-opacity-30 transition-all duration-300" onclick="navigateToPage('home')">
                    <i class="fas fa-arrow-left text-xl text-white"></i>
                </div>
                <h1 class="text-xl font-bold text-white">申报单查询</h1>
            </div>

            <div class="px-6 py-6 bg-gray-50 min-h-screen">
                <!-- 搜索栏 -->
                <div class="bg-white rounded-2xl p-5 shadow-lg mb-6">
                    <div class="flex space-x-3">
                        <input type="text" class="flex-1 px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-light-blue focus:border-transparent transition-all duration-300 bg-gray-50 focus:bg-white" placeholder="输入设备编号或使用单位">
                        <button class="bg-gradient-to-r from-light-blue to-soft-blue text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- 筛选标签 -->
                <div class="flex space-x-3 mb-6 overflow-x-auto pb-2">
                    <button class="bg-gradient-to-r from-primary-blue to-light-blue text-white px-5 py-2 rounded-full text-sm whitespace-nowrap font-semibold shadow-lg">全部</button>
                    <button class="bg-white text-gray-700 px-5 py-2 rounded-full text-sm whitespace-nowrap font-medium border-2 border-gray-200 hover:border-orange-400 hover:text-orange-600 transition-all duration-300">待审核</button>
                    <button class="bg-white text-gray-700 px-5 py-2 rounded-full text-sm whitespace-nowrap font-medium border-2 border-gray-200 hover:border-green-400 hover:text-green-600 transition-all duration-300">已通过</button>
                    <button class="bg-white text-gray-700 px-5 py-2 rounded-full text-sm whitespace-nowrap font-medium border-2 border-gray-200 hover:border-red-400 hover:text-red-600 transition-all duration-300">已拒绝</button>
                </div>

                <!-- 申报列表 -->
                <div class="space-y-4">
                    <div class="bg-white rounded-2xl p-5 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 cursor-pointer" onclick="showReportDetail('001')">
                        <div class="flex justify-between items-start mb-3">
                            <h4 class="font-bold text-gray-800">设备编号: EL001</h4>
                            <span class="bg-gradient-to-r from-orange-400 to-orange-500 text-white px-3 py-1 rounded-full text-xs font-semibold animate-pulse">待审核</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2 flex items-center">
                            <i class="fas fa-building text-gray-400 mr-2"></i>
                            使用单位: 万达广场
                        </p>
                        <p class="text-sm text-gray-600 mb-3 flex items-center">
                            <i class="fas fa-clock text-gray-400 mr-2"></i>
                            申报时间: 2024-01-15 10:30
                        </p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-500 bg-blue-50 px-3 py-1 rounded-full font-medium">乘客电梯</span>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-5 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 cursor-pointer" onclick="showReportDetail('002')">
                        <div class="flex justify-between items-start mb-3">
                            <h4 class="font-bold text-gray-800">设备编号: EL002</h4>
                            <span class="bg-gradient-to-r from-green-400 to-green-500 text-white px-3 py-1 rounded-full text-xs font-semibold">已通过</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2 flex items-center">
                            <i class="fas fa-building text-gray-400 mr-2"></i>
                            使用单位: 银泰中心
                        </p>
                        <p class="text-sm text-gray-600 mb-3 flex items-center">
                            <i class="fas fa-clock text-gray-400 mr-2"></i>
                            申报时间: 2024-01-14 14:20
                        </p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-500 bg-blue-50 px-3 py-1 rounded-full font-medium">载货电梯</span>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 消息通知页面 -->
        <div id="notifications" class="page hidden">
            <div class="bg-gradient-to-r from-accent-blue to-primary-blue px-6 py-4 flex items-center shadow-lg">
                <div class="bg-white bg-opacity-20 p-2 rounded-full mr-4 cursor-pointer hover:bg-opacity-30 transition-all duration-300" onclick="navigateToPage('home')">
                    <i class="fas fa-arrow-left text-xl text-white"></i>
                </div>
                <h1 class="text-xl font-bold text-white">消息通知</h1>
                <div class="ml-auto">
                    <span class="bg-red-500 text-white text-sm px-3 py-1 rounded-full font-bold">8条未读</span>
                </div>
            </div>

            <div class="px-6 py-6 bg-gray-50 min-h-screen">
                <div class="space-y-4">
                    <div class="bg-white rounded-2xl p-5 shadow-lg border-l-4 border-red-400">
                        <div class="flex items-start justify-between mb-2">
                            <h4 class="font-bold text-gray-800">审核结果通知</h4>
                            <span class="text-xs text-gray-500">2分钟前</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">您的检验申报(EL001)审核未通过，请查看详细意见并重新提交。</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs bg-red-50 text-red-600 px-2 py-1 rounded-full">紧急</span>
                            <button class="text-primary-blue text-sm font-medium">查看详情</button>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-5 shadow-lg border-l-4 border-green-400">
                        <div class="flex items-start justify-between mb-2">
                            <h4 class="font-bold text-gray-800">检测完成通知</h4>
                            <span class="text-xs text-gray-500">1小时前</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">设备EL002的检测已完成，合格证已生成，请及时下载。</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs bg-green-50 text-green-600 px-2 py-1 rounded-full">完成</span>
                            <button class="text-primary-blue text-sm font-medium">下载证书</button>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl p-5 shadow-lg border-l-4 border-blue-400">
                        <div class="flex items-start justify-between mb-2">
                            <h4 class="font-bold text-gray-800">系统维护通知</h4>
                            <span class="text-xs text-gray-500">3小时前</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">系统将于今晚22:00-24:00进行维护升级，期间服务可能中断。</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs bg-blue-50 text-blue-600 px-2 py-1 rounded-full">通知</span>
                            <button class="text-primary-blue text-sm font-medium">知道了</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面导航功能
        function navigateToPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.add('hidden');
            });

            // 显示目标页面
            document.getElementById(pageId).classList.remove('hidden');

            // 添加页面切换动画
            const targetPage = document.getElementById(pageId);
            targetPage.style.opacity = '0';
            targetPage.style.transform = 'translateX(20px)';

            setTimeout(() => {
                targetPage.style.transition = 'all 0.3s ease-out';
                targetPage.style.opacity = '1';
                targetPage.style.transform = 'translateX(0)';
            }, 10);
        }

        // 显示通知页面
        function showNotifications() {
            navigateToPage('notifications');
        }

        // 显示申报详情
        function showReportDetail(reportId) {
            // 这里可以根据reportId加载不同的详情数据
            alert(`查看申报详情: ${reportId}`);
        }

        // 提交检验申报
        function submitInspection() {
            const button = event.target;
            const originalText = button.innerHTML;

            // 显示加载状态
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>提交中...';
            button.disabled = true;
            button.classList.add('opacity-75');

            // 模拟提交过程
            setTimeout(() => {
                // 显示成功状态
                button.innerHTML = '<i class="fas fa-check mr-2"></i>提交成功';
                button.classList.remove('opacity-75');
                button.classList.add('bg-green-500');

                setTimeout(() => {
                    alert('申报提交成功！申报编号：EL' + Date.now().toString().slice(-6));
                    button.innerHTML = originalText;
                    button.disabled = false;
                    button.classList.remove('bg-green-500');
                    navigateToPage('home');
                }, 1000);
            }, 2000);
        }

        // 保存草稿
        function saveDraft() {
            const button = event.target;
            const originalText = button.innerHTML;

            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>保存中...';
            button.disabled = true;

            setTimeout(() => {
                button.innerHTML = '<i class="fas fa-check mr-2"></i>已保存';
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }, 1000);
            }, 1000);
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示首页
            navigateToPage('home');

            // 添加触摸反馈
            document.querySelectorAll('.cursor-pointer').forEach(element => {
                element.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                });

                element.addEventListener('touchend', function() {
                    this.style.transform = '';
                });
            });

            // 添加输入框焦点动画
            document.querySelectorAll('input, select').forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('transform', 'scale-105');
                });

                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('transform', 'scale-105');
                });
            });
        });

        // 防止页面缩放
        document.addEventListener('gesturestart', function (e) {
            e.preventDefault();
        });

        document.addEventListener('gesturechange', function (e) {
            e.preventDefault();
        });

        document.addEventListener('gestureend', function (e) {
            e.preventDefault();
        });
    </script>

    <style>
        /* iPhone 16 Pro Max 适配 */
        @media screen and (max-width: 430px) {
            .grid-cols-3 > div {
                min-height: 85px;
            }

            .grid-cols-2 > div {
                min-height: 75px;
            }
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }

        /* 页面切换动画 */
        .page {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .page.hidden {
            display: none !important;
        }

        /* 输入框焦点效果 */
        input:focus, select:focus {
            box-shadow: 0 0 0 4px rgba(30, 64, 175, 0.1);
            transform: translateY(-1px);
        }

        /* 按钮点击效果 */
        button:active {
            transform: scale(0.95) !important;
        }

        /* 卡片悬停效果增强 */
        .group:hover .group-hover\\:scale-110 {
            transform: scale(1.1);
        }

        /* 脉冲动画优化 */
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.05);
            }
        }

        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        /* 渐变文字效果 */
        .gradient-text {
            background: linear-gradient(135deg, #1E40AF 0%, #3B82F6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 玻璃态效果 */
        .backdrop-blur-sm {
            backdrop-filter: blur(4px);
        }

        /* 安全区域适配 */
        body {
            padding-bottom: env(safe-area-inset-bottom);
        }

        /* 防止文字选择 */
        .cursor-pointer {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* 优化触摸体验 */
        .cursor-pointer {
            -webkit-tap-highlight-color: transparent;
        }

        /* 响应式字体 */
        @media (max-width: 375px) {
            .text-xl {
                font-size: 1.125rem;
            }

            .text-lg {
                font-size: 1rem;
            }
        }

        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
        }

        /* 优化动画性能 */
        * {
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
        }
    </style>
</body>
</html>
